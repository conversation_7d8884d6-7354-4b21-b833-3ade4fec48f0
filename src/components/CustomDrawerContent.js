// CustomDrawerContent.js
import React, {useState, useEffect, useRef} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Switch,
  Animated,
  Pressable,
  Dimensions,
} from 'react-native';
import {DrawerContentScrollView} from '@react-navigation/drawer';
import {Images} from '../assets';
import {FONTS, COLORS} from '../common/constant';

const SWITCH_WIDTH = 95;
const BUTTON_WIDTH = SWITCH_WIDTH / 2;

export default function CustomDrawerContent({props, navigation}) {
  const [isEnglish, setIsEnglish] = useState(true);
  const [isOn, setIsOn] = useState(false);
  const [language, setLanguage] = useState('EN');
  const animation = useRef(new Animated.Value(0)).current;

  const toggleLanguage = () => {
    setIsEnglish(prev => !prev);
    // Add logic to update the app's language here
  };
  // Animate pill position when language changes
  useEffect(() => {
    Animated.timing(animation, {
      toValue: language === 'EN' ? 0 : BUTTON_WIDTH,
      duration: 200,
      useNativeDriver: false,
    }).start();
  }, [language]);

  const drawerItems = [
    {label: 'Categories', icon: Images.ic_caretRight, navigateTo: 'Categories'},
    {
      label: 'History',
      icon: Images.ic_caretRight,
      navigateTo: 'HistoryListScreen',
    },
    {
      label: 'Report/Feedback',
      icon: Images.ic_caretRight,
      navigateTo: 'ReportScreen',
    },
    {
      label: 'Terms of Service',
      icon: Images.ic_caretRight,
      navigateTo: 'TermsScreen',
    },
    {
      label: 'Privacy Policy',
      icon: Images.ic_caretRight,
      navigateTo: 'PrivacyPolicyScreen',
    },
  ];

  return (
    <View style={{flex: 1}}>
      <View style={styles.closeButtonContainer}>
        <TouchableOpacity onPress={() => navigation.closeDrawer()}>
          <Image source={Images.ic_close} style={styles.drawerIcon} />
        </TouchableOpacity>
      </View>
      <DrawerContentScrollView contentContainerStyle={{paddingTop: 5}}>
        {drawerItems.map((item, index) => (
          <TouchableOpacity
            key={index}
            onPress={() => navigation.navigate(item.navigateTo)}
            style={styles.drawerItem}>
            <Text style={styles.drawerLabel}>{item.label}</Text>
            <Image source={item.icon} style={styles.drawerIcon} />
          </TouchableOpacity>
        ))}
        <View style={styles.drawerItem}>
          <Text style={styles.drawerLabel}>Language</Text>
          <View style={styles.switchContainer}>
            {/* Animated background pill */}
            <Animated.View
              style={[
                styles.activePill,
                {transform: [{translateX: animation}]},
              ]}
            />

            {/* Buttons */}
            {['EN', 'HI'].map(lang => (
              <Pressable
                key={lang}
                onPress={() => setLanguage(lang)}
                style={styles.button}>
                <Text
                  style={[
                    styles.buttonText,
                    language === lang && styles.activeText,
                  ]}>
                  {lang}
                </Text>
              </Pressable>
            ))}
          </View>
        </View>
      </DrawerContentScrollView>
      <Text style={styles.versionText}>App Version v1.0</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  closeButtonContainer: {
    alignItems: 'flex-end',
    padding: 20,
    marginTop: 30,
  },
  drawerItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderColor: '#D9D9D9',
  },
  drawerLabel: {
    fontSize: 16,
    fontFamily: FONTS.POPPINS.MEDIUM,
    color: COLORS.BLACK,
  },
  drawerIcon: {
    width: 25,
    height: 25,
    tintColor: COLORS.BLACK,
  },
  switchContainer: {
    width: SWITCH_WIDTH,
    height: 36,
    backgroundColor: '#d9dde4',
    borderRadius: 8,
    flexDirection: 'row',
    position: 'relative',
    overflow: 'hidden',
  },
  activePill: {
    position: 'absolute',
    width: BUTTON_WIDTH - 4,
    height: '90%',
    backgroundColor: '#0d1b3f',
    borderRadius: 8,
    margin: 2,
  },
  button: {
    width: BUTTON_WIDTH,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  buttonText: {
    color: '#333',
    fontWeight: '500',
  },
  activeText: {
    color: '#fff',
  },
  versionText: {
    fontSize: 14,
    fontFamily: FONTS.POPPINS.REGULAR,
    margin: 30,
    textAlign: 'center',
  },
});

{
  /*
  {
      label: 'Preference',
      icon: Images.ic_caretRight,
      navigateTo: 'SelectCategories',
    },
  */
}
